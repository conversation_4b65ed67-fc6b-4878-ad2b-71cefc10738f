// File: app/(tabs)/_layout.tsx

import React from 'react';
import { Tabs } from 'expo-router';
import { FontAwesome, Ionicons, MaterialIcons } from '@expo/vector-icons'; // Choose your preferred icon sets
import { TabBar } from '@/components/TabBar';

// You can explore the built-in icon families and names here: https://icons.expo.fyi/
// Or use a custom SVG icon component.

export default function TabLayout() {
  return (
    <Tabs
      tabBar={props => <TabBar {...props}/>}
      
      screenOptions={{
        // ... your existing global screenOptions
        tabBarActiveTintColor: '#5A67D8',
        tabBarInactiveTintColor: '#8e8e93',
        tabBarStyle: {
          backgroundColor: '#1c1c1e',
          borderTopColor: '#3a3a3c',
        },
        // Default header styles (will apply if a screen doesn't hide its header)
        headerStyle: {
          backgroundColor: '#1c1c1e',
        },
        headerTintColor: '#ffffff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
      >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Explore',
          headerShown: false, // <<< HIDE THE DEFAULT HEADER FOR THIS TAB
        }}
      />
      <Tabs.Screen
        name="create"
        options={{
          title: 'Create'
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile'
        }}
      />
    </Tabs>
  );
}