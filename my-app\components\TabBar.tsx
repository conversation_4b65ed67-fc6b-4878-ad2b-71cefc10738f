import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { useState } from 'react';
import { LayoutChangeEvent, StyleSheet, View } from 'react-native';
import Animated, { interpolate, useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import TabBarButton from './TabBarButton';

export function TabBar({ state, descriptors, navigation }: BottomTabBarProps) {
 
  const [dimensions, setDimensions] = useState({height: 0, width: 0});

  const buttonWidth = dimensions.width / state.routes.length;

  const onTabbarLayout = (event: LayoutChangeEvent) => {
        setDimensions({
          height: event.nativeEvent.layout.height,
          width: event.nativeEvent.layout.width
        });
  };

  const tabPositionX = useSharedValue(0);
  const isCreateTabFocused = useSharedValue(0); // 0 = false, 1 = true

  const animatedStyle = useAnimatedStyle(() => {
    // Make background circular when create tab is focused
    const backgroundHeight = dimensions.height - 25;
    const backgroundWidth = buttonWidth - 25;
    const baseCircularSize = Math.min(backgroundHeight, backgroundWidth);
    const enhancedCircularSize = baseCircularSize * 1.3;

    // Use interpolation for smooth transitions
    const animatedHeight = interpolate(
      isCreateTabFocused.value,
      [0, 1],
      [backgroundHeight, enhancedCircularSize]
    );

    const animatedWidth = interpolate(
      isCreateTabFocused.value,
      [0, 1],
      [backgroundWidth, enhancedCircularSize]
    );

    const animatedBorderRadius = interpolate(
      isCreateTabFocused.value,
      [0, 1],
      [25, enhancedCircularSize / 2]
    );

    // Calculate centering offset with interpolation for smooth movement
    const widthDifference = backgroundWidth - enhancedCircularSize;
    const centeringOffset = interpolate(
      isCreateTabFocused.value,
      [0, 1],
      [0, widthDifference / 2]
    );

    return {
      transform: [{translateX: tabPositionX.value + centeringOffset}],
      height: animatedHeight,
      width: animatedWidth,
      borderRadius: animatedBorderRadius,
    }
  });

  return (
    <View onLayout={onTabbarLayout} style={styles.tabbar}>
      <Animated.View style={[animatedStyle, {
        position: 'absolute',
        backgroundColor: 'green',
        marginHorizontal: 12,
        left: 0
      }]}/>

      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
              ? options.title
              : route.name;

        const isFocused = state.index === index;

        const onPress = () => {

          tabPositionX.value = withSpring(buttonWidth * index, {
            duration: 2500
          });

          // Update create tab focus state
          isCreateTabFocused.value = withSpring(route.name === 'create' ? 1 : 0, {
            duration: 350
          });

          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          console.log(dimensions.width, buttonWidth * index);

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        const onLongPress = () => {
            navigation.emit({
                type: 'tabLongPress',
                target: route.key
            });
        };

        return (
        <TabBarButton
        key={route.name}
        onPress={onPress} 
        onLongPress={onLongPress}
        isFocused={isFocused}
        routeName={route.name}
        color={isFocused ? "green" : "black"}
        label={typeof label === 'string' ? label : route.name}
        />
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create ({
    tabbar: {
        position: 'absolute',
        bottom: 10,
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        backgroundColor: 'white',
        marginHorizontal: 80,
        paddingVertical: 10,
        borderRadius: 35,
        shadowColor: 'black',
        shadowOffset: {width: 0, height: 10},
        shadowRadius: 20,
        shadowOpacity: 0.1
    }
})